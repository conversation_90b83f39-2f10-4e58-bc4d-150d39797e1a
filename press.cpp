w#include <iostream>
#include <windows.h>
#include <random>
#include <thread>
#include <chrono>

#pragma comment(lib, "user32.lib")

class KeyPresser {
private:
    std::random_device rd;
    std::mt19937 gen;
    std::uniform_real_distribution<> dis;

public:
    KeyPresser() : gen(rd()), dis(1.5, 2.0) {}

    void PressKey(char key) {
        INPUT input = { 0 };
        input.type = INPUT_KEYBOARD;

        // Karakteri virtual key code'a çevir
        SHORT vk = VkKeyScan(key);
        input.ki.wVk = vk & 0xFF;

        // Tuşa bas
        input.ki.dwFlags = 0;
        UINT result1 = SendInput(1, &input, sizeof(INPUT));

        Sleep(20); // Kısa bekleme

        // Tuşu bırak
        input.ki.dwFlags = KEYEVENTF_KEYUP;
        UINT result2 = SendInput(1, &input, sizeof(INPUT));

        // Debug için
        if (result1 == 0 || result2 == 0) {
            std::cout << "Tuş basma hatası: " << key << " (Error: " << GetLastError() << ")\n";
        }

        Sleep(30); // Tuşlar arası bekleme
    }

    void WaitRandomInterval() {
        double seconds = dis(gen);
        int milliseconds = static_cast<int>(seconds * 1000);
        Sleep(milliseconds);
    }

    void ExecuteSequence() {
        std::cout << "W tuşuna 50 defa basılıyor...\n";

        // 50 defa W tuşuna bas
        for (int i = 0; i < 50; i++) {
            PressKey('W');
            std::cout << "W basıldı: " << (i + 1) << "/50\n";

            if (i < 49) { // Son basımdan sonra bekleme
                WaitRandomInterval();
            }
        }

        std::cout << "\nS tuşuna 50 defa basılıyor...\n";

        // 50 defa S tuşuna bas
        for (int i = 0; i < 50; i++) {
            PressKey('S');
            std::cout << "S basıldı: " << (i + 1) << "/50\n";

            if (i < 49) { // Son basımdan sonra bekleme
                WaitRandomInterval();
            }
        }

        std::cout << "\nSekans tamamlandı!\n";
    }
};

int main() {
    // Console başlığını ayarla
    SetConsoleTitle(L"Ekran Koruma Önleyici");

    std::cout << "=== Ekran Koruma Önleyici ===\n";
    std::cout << "X tuşuna basın ve sekans başlasın.\n";
    std::cout << "Çıkmak için Ctrl+C kullanın.\n";
    std::cout << "Not: Uygulama aktif pencerede tuşlara basacak!\n\n";

    KeyPresser presser;
    bool isRunning = false;

    while (true) {
        // X tuşuna basılıp basılmadığını kontrol et
        if ((GetAsyncKeyState('X') & 0x8000) && !isRunning) {
            std::cout << "\nX tuşu algılandı! Sekans başlıyor...\n";
            std::cout << "Lütfen hedef uygulamaya geçin (3 saniye bekleniyor)...\n";

            isRunning = true;

            // Kullanıcının hedef uygulamaya geçmesi için zaman ver
            Sleep(3000);

            presser.ExecuteSequence();

            std::cout << "\nSekans tamamlandı! Tekrar X tuşuna basabilirsiniz...\n\n";
            isRunning = false;

            // X tuşunun serbest bırakılmasını bekle
            while (GetAsyncKeyState('X') & 0x8000) {
                Sleep(50);
            }
        }

        // CPU kullanımını azaltmak için kısa bekleme
        Sleep(100);
    }

    return 0;
}
