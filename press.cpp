#include <iostream>
#include <windows.h>
#include <random>

int main() {
    std::cout << "X tusuna basin, sonsuz dongu baslasin.\n";
    std::cout << "Tekrar X'e basin, dursun.\n";
    std::cout << "Cikmak icin Ctrl+C kullanin.\n\n";

    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(1500, 3000); // 1.5-2 saniye arası (milisaniye)

    bool isRunning = false;

    while (true) {
        if (GetAsyncKeyState('X') & 0x8000) {
            // X tuşunun bırakılmasını bekle
            while (GetAsyncKeyState('X') & 0x8000) {
                Sleep(50);
            }

            if (!isRunning) {
                std::cout << "X basildi! Sonsuz dongu basliyor...\n";
                std::cout << "Durdurmak icin tekrar X'e basin.\n\n";
                isRunning = true;
            } else {
                std::cout << "X basildi! Dongu durduruluyor...\n\n";
                isRunning = false;
            }
        }

        if (isRunning) {
            // 20 defa W
            for (int i = 0; i < 17 && isRunning; i++) {
                // X tuşu kontrol et
                if (GetAsyncKeyState('X') & 0x8000) {
                    while (GetAsyncKeyState('X') & 0x8000) Sleep(50);
                    std::cout << "\nX basildi! Dongu durduruluyor...\n\n";
                    isRunning = false;
                    break;
                }

                keybd_event('W', 0, 0, 0);           // W'ye bas
                Sleep(500);                           // 0.5 saniye basılı tut
                keybd_event('W', 0, KEYEVENTF_KEYUP, 0); // W'yi bırak

                std::cout << "W " << (i+1) << "/20\n";

                if (i < 19 && isRunning) {
                    int wait_time = dis(gen);
                    // Bekleme sırasında da X tuşunu kontrol et
                    for (int j = 0; j < wait_time / 100 && isRunning; j++) {
                        if (GetAsyncKeyState('X') & 0x8000) {
                            while (GetAsyncKeyState('X') & 0x8000) Sleep(50);
                            std::cout << "\nX basildi! Dongu durduruluyor...\n\n";
                            isRunning = false;
                            break;
                        }
                        Sleep(100);
                    }
                }
            }

            if (isRunning) {
                std::cout << "\n";

                // 20 defa S
                for (int i = 0; i < 17 && isRunning; i++) {
                    // X tuşu kontrol et
                    if (GetAsyncKeyState('X') & 0x8000) {
                        while (GetAsyncKeyState('X') & 0x8000) Sleep(50);
                        std::cout << "\nX basildi! Dongu durduruluyor...\n\n";
                        isRunning = false;
                        break;
                    }

                    keybd_event('S', 0, 0, 0);           // S'ye bas
                    Sleep(500);                           // 0.5 saniye basılı tut
                    keybd_event('S', 0, KEYEVENTF_KEYUP, 0); // S'yi bırak

                    std::cout << "S " << (i+1) << "/20\n";

                    if (i < 19 && isRunning) {
                        int wait_time = dis(gen);
                        // Bekleme sırasında da X tuşunu kontrol et
                        for (int j = 0; j < wait_time / 100 && isRunning; j++) {
                            if (GetAsyncKeyState('X') & 0x8000) {
                                while (GetAsyncKeyState('X') & 0x8000) Sleep(50);
                                std::cout << "\nX basildi! Dongu durduruluyor...\n\n";
                                isRunning = false;
                                break;
                            }
                            Sleep(100);
                        }
                    }
                }

                if (isRunning) {
                    std::cout << "\nDongu tamamlandi, tekrar basliyor...\n\n";
                }
            }
        }

        Sleep(100);
    }

    return 0;
}
