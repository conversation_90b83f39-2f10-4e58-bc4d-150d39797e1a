#include <iostream>
#include <windows.h>
#include <random>
#include <thread>
#include <chrono>

class KeyPresser {
private:
    std::random_device rd;
    std::mt19937 gen;
    std::uniform_real_distribution<> dis;

public:
    KeyPresser() : gen(rd()), dis(1.5, 2.0) {}

    void PressKey(WORD key) {
        INPUT input = { 0 };
        input.type = INPUT_KEYBOARD;
        input.ki.wVk = key;

        // Tuşa bas
        input.ki.dwFlags = 0;
        SendInput(1, &input, sizeof(INPUT));

        // Kısa bekleme
        Sleep(50);

        // Tuşu bırak
        input.ki.dwFlags = KEYEVENTF_KEYUP;
        SendInput(1, &input, sizeof(INPUT));
    }

    void WaitRandomInterval() {
        double seconds = dis(gen);
        int milliseconds = static_cast<int>(seconds * 1000);
        Sleep(milliseconds);
    }

    void ExecuteSequence() {
        std::cout << "W tuşuna 50 defa basılıyor...\n";

        // 50 defa W tuşuna bas
        for (int i = 0; i < 50; i++) {
            PressKey('W');
            std::cout << "W basıldı: " << (i + 1) << "/50\n";

            if (i < 49) { // Son basımdan sonra bekleme
                WaitRandomInterval();
            }
        }

        std::cout << "\nS tuşuna 50 defa basılıyor...\n";

        // 50 defa S tuşuna bas
        for (int i = 0; i < 50; i++) {
            PressKey('S');
            std::cout << "S basıldı: " << (i + 1) << "/50\n";

            if (i < 49) { // Son basımdan sonra bekleme
                WaitRandomInterval();
            }
        }

        std::cout << "\nSekans tamamlandı!\n";
    }
};

int main() {
    std::cout << "=== Ekran Koruma Önleyici ===\n";
    std::cout << "X tuşuna basın ve sekans başlasın.\n";
    std::cout << "Çıkmak için Ctrl+C kullanın.\n\n";

    KeyPresser presser;

    while (true) {
        // X tuşuna basılıp basılmadığını kontrol et
        if (GetAsyncKeyState('X') & 0x8000) {
            std::cout << "\nX tuşu algılandı! Sekans başlıyor...\n";

            // Tuşun tekrar basılmasını önlemek için kısa bekleme
            Sleep(500);

            presser.ExecuteSequence();

            std::cout << "\nTekrar X tuşuna basabilirsiniz...\n\n";
        }

        // CPU kullanımını azaltmak için kısa bekleme
        Sleep(100);
    }

    return 0;
}
