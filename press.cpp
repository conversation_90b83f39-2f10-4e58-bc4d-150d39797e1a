#include <iostream>
#include <windows.h>
#include <random>

int main() {
    std::cout << "X tusuna basin, W ve S tuslarina basacak.\n";
    std::cout << "Cikmak icin Ctrl+C kullanin.\n\n";

    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(1500, 2000); // 1.5-2 saniye arası (milisaniye)

    while (true) {
        if (GetAsyncKeyState('X') & 0x8000) {
            std::cout << "X basildi! Basliyor...\n";

            // X tuşunun bırakılmasını bekle
            while (GetAsyncKeyState('X') & 0x8000) {
                Sleep(50);
            }

            // 20 defa W
            for (int i = 0; i < 20; i++) {
                keybd_event('W', 0, 0, 0);           // W'ye bas
                Sleep(500);                           // 0.5 saniye basılı tut
                keybd_event('W', 0, KEYEVENTF_KEYUP, 0); // W'yi bırak

                std::cout << "W " << (i+1) << "/20\n";

                if (i < 19) {
                    int wait_time = dis(gen);
                    Sleep(wait_time);
                }
            }

            std::cout << "\n";

            // 20 defa S
            for (int i = 0; i < 20; i++) {
                keybd_event('S', 0, 0, 0);           // S'ye bas
                Sleep(500);                           // 0.5 saniye basılı tut
                keybd_event('S', 0, KEYEVENTF_KEYUP, 0); // S'yi bırak

                std::cout << "S " << (i+1) << "/20\n";

                if (i < 19) {
                    int wait_time = dis(gen);
                    Sleep(wait_time);
                }
            }

            std::cout << "\nTamamlandi!\n\n";
        }

        Sleep(100);
    }

    return 0;
}
